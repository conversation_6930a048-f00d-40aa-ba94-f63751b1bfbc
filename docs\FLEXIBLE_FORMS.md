# Flexible Forms Feature

This document describes the flexible forms feature that allows event administrators to customize registration forms for each event.

## Overview

The flexible forms system allows admins to:
- Enable/disable predefined standard fields per event
- Create custom fields with various input types
- Configure validation rules and field options
- Dynamically render registration forms based on configuration

## Architecture

### Database Schema

#### `form_fields` Table
Stores field configurations for each event:
- `id`: Unique field identifier
- `event_id`: Reference to the event
- `field_key`: Unique key for the field (e.g., 'first_name', 'custom_dietary')
- `field_label`: Display label for the field
- `field_type`: Type of input (text, email, phone, select, checkbox, etc.)
- `field_options`: JSON configuration for select/checkbox options
- `is_required`: Whether the field is required
- `is_standard_field`: Whether this is a predefined standard field
- `is_enabled`: Whether the field is active
- `display_order`: Order in which fields appear
- `validation_rules`: JSON validation configuration
- `placeholder_text`: Placeholder text for inputs
- `help_text`: Additional help text

#### `form_responses` Table
Stores user responses to form fields:
- `id`: Unique response identifier
- `event_id`: Reference to the event
- `guest_id`: Reference to the guest/user
- `field_key`: Key of the field being responded to
- `field_value`: The user's response value

### Standard Fields

The system includes predefined standard fields:
- `first_name`: First Name (text, required by default)
- `middle_name`: Middle Name (text, optional)
- `last_name`: Last Name (text, required by default)
- `full_name`: Full Name (text, alternative to first/last)
- `email`: Email Address (email, required by default)
- `mobile`: Mobile Number (phone, optional)
- `whatsapp`: WhatsApp Number (phone, optional)
- `organization`: Company/Organization (text, optional)
- `designation`: Designation/Role (text, optional)

### Field Types

Supported field types:
- `text`: Single-line text input
- `email`: Email input with validation
- `phone`: Phone number input
- `url`: URL input with validation
- `number`: Numeric input
- `date`: Date picker
- `textarea`: Multi-line text input
- `select`: Dropdown selection
- `checkbox`: Checkbox input (single or multiple)

## Usage

### Admin Interface

#### Accessing Form Builder
1. Navigate to event dashboard
2. Click "Form Builder" tab
3. Configure standard and custom fields

#### Managing Standard Fields
- Toggle standard fields on/off per event
- Configure field requirements and validation
- Customize labels and help text

#### Creating Custom Fields
1. Click "Add Custom Field"
2. Configure field properties:
   - Label and type
   - Required/optional status
   - Validation rules
   - Options (for select/checkbox)
   - Help text and placeholders

### API Endpoints

#### Form Field Management
- `GET /api/events/{eventId}/form-fields` - Get all fields for an event
- `POST /api/events/{eventId}/form-fields` - Create a new custom field
- `PATCH /api/events/{eventId}/form-fields/{fieldId}` - Update a field
- `DELETE /api/events/{eventId}/form-fields/{fieldId}` - Delete a custom field

#### Form Responses
- `POST /api/events/{eventId}/form-responses` - Submit form responses
- `GET /api/events/{eventId}/form-responses` - Get responses (admin only)

### Frontend Components

#### Admin Components
- `FormFieldManager.svelte`: Main form builder interface
- `StandardFieldToggle.svelte`: Toggle for standard fields
- `CustomFieldBuilder.svelte`: Custom field creation modal
- `FieldEditor.svelte`: Field editing modal

#### Registration Components
- `DynamicRegistrationForm.svelte`: Main registration form
- `DynamicFormField.svelte`: Individual field renderer

## Implementation Details

### Dynamic Validation
The system generates Zod schemas dynamically based on field configurations:
```typescript
const schema = generateDynamicSchema(formFields);
const validation = schema.safeParse(formData);
```

### Field Rendering
Fields are rendered based on their type and configuration:
```svelte
{#if field.fieldType === 'text'}
  <Input {...validationAttributes} />
{:else if field.fieldType === 'select'}
  <Select options={field.fieldOptions} />
{/if}
```

### Data Storage
Form responses are stored in a flexible key-value structure:
```typescript
{
  eventId: "uuid",
  guestId: 123,
  responses: {
    "first_name": "John",
    "email": "<EMAIL>",
    "custom_dietary": "Vegetarian"
  }
}
```

## Migration

### Existing Events
Run the migration script to initialize standard fields for existing events:
```bash
npm run migrate:form-fields
```

### Backward Compatibility
The system maintains backward compatibility with existing hardcoded forms by:
- Falling back to the old schema if no form fields are configured
- Mapping existing guest table columns to form responses
- Preserving existing registration workflow

## Security Considerations

- Field keys are sanitized to prevent injection attacks
- Validation rules are applied server-side
- Admin-only access to form configuration
- Row Level Security (RLS) policies on database tables

## Testing

### Development Testing
Use the test API endpoint:
```bash
POST /api/test-form-fields
{
  "action": "initialize",
  "eventId": "your-event-id"
}
```

### Form Preview
Access form preview mode by adding `?preview=true` to the event URL.

## Future Enhancements

Potential improvements:
- Drag-and-drop field reordering
- Conditional field logic
- File upload fields
- Integration with external form builders
- Advanced validation rules
- Field templates and presets
