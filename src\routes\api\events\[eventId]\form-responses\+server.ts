// API routes for form response submission

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { saveFormResponses, validateFormData } from '$lib/server/formResponses';
import { getEnabledFormFields } from '$lib/server/formFields';
import type { FormSubmissionData, DynamicFormData } from '$lib/types/formFields';
import { getRegistrationStatus } from '$lib/server/registrationStatus';

// POST /api/events/[eventId]/form-responses
export const POST: RequestHandler = async ({ params, request, locals: { supabase } }) => {
  try {
    const { eventId } = params;

    // Check registration status (including automatic closure and capacity)
    const registrationStatus = await getRegistrationStatus(supabase, eventId);

    if (!registrationStatus.isOpen) {
      return json({ error: registrationStatus.reason || 'Registration is closed' }, { status: 400 });
    }

    // Parse request body
    const { responses }: { eventId: string; responses: DynamicFormData } = await request.json();

    if (!responses || typeof responses !== 'object') {
      return json({ error: 'Invalid form data' }, { status: 400 });
    }

    // Get form fields for validation
    const fieldsResult = await getEnabledFormFields(eventId);
    if (fieldsResult.error || !fieldsResult.data) {
      return json({ error: 'Failed to load form configuration' }, { status: 500 });
    }

    const formFields = fieldsResult.data;

    // Validate form data
    const validationResult = await validateFormData(eventId, responses);
    if (!validationResult.isValid) {
      return json({
        error: 'Form validation failed',
        validationErrors: validationResult.errors
      }, { status: 400 });
    }

    // Check for duplicate email submissions (if email field exists)
    const emailField = formFields.find(f => f.fieldKey === 'email');
    if (emailField && responses.email) {
      const { data: existingGuest, error: duplicateError } = await supabase
        .from('guests')
        .select('id')
        .eq('event_id', eventId)
        .eq('email', responses.email)
        .single();

      if (existingGuest) {
        return json({ error: 'A registration with this email already exists' }, { status: 400 });
      }
    }

    // Save form responses
    const submissionData: FormSubmissionData = {
      eventId,
      responses
    };

    const saveResult = await saveFormResponses(submissionData);

    if (saveResult.error) {
      return json({ error: saveResult.error }, { status: 500 });
    }

    // TODO: Send confirmation email, generate QR code, etc.
    // This would integrate with the existing registration flow

    return json({
      success: true,
      guestId: saveResult.guestId,
      message: 'Registration completed successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/events/[eventId]/form-responses:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

// GET /api/events/[eventId]/form-responses (for admin)
export const GET: RequestHandler = async ({ params, locals: { supabase } }) => {
  try {
    const { eventId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Get form responses (this would be implemented in formResponses.ts)
    // For now, return a placeholder
    return json({ data: [] });

  } catch (error) {
    console.error('Error in GET /api/events/[eventId]/form-responses:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
