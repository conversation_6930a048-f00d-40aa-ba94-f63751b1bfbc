// API routes for individual form field management

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { updateFormField, deleteForm<PERSON>ield } from '$lib/server/formFields';
import type { FormFieldUpdate } from '$lib/types/formFields';

// PATCH /api/events/[eventId]/form-fields/[fieldId]
export const PATCH: RequestHandler = async ({ params, request, locals: { supabase } }) => {
  try {
    const { eventId, fieldId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Verify field exists and belongs to this event
    const { data: fieldData, error: fieldError } = await supabase
      .from('form_fields')
      .select('id, event_id')
      .eq('id', fieldId)
      .eq('event_id', eventId)
      .single();

    if (fieldError || !fieldData) {
      return json({ error: 'Field not found' }, { status: 404 });
    }

    // Parse request body
    const updates: FormFieldUpdate = await request.json();

    // Update the field
    const result = await updateFormField(fieldId, updates);
    
    if (result.error) {
      return json({ error: result.error }, { status: 500 });
    }

    return json({ data: result.data });
  } catch (error) {
    console.error('Error in PATCH /api/events/[eventId]/form-fields/[fieldId]:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

// DELETE /api/events/[eventId]/form-fields/[fieldId]
export const DELETE: RequestHandler = async ({ params, locals: { supabase } }) => {
  try {
    const { eventId, fieldId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Verify field exists, belongs to this event, and is not a standard field
    const { data: fieldData, error: fieldError } = await supabase
      .from('form_fields')
      .select('id, event_id, is_standard_field')
      .eq('id', fieldId)
      .eq('event_id', eventId)
      .single();

    if (fieldError || !fieldData) {
      return json({ error: 'Field not found' }, { status: 404 });
    }

    if (fieldData.is_standard_field) {
      return json({ error: 'Cannot delete standard fields' }, { status: 400 });
    }

    // Delete the field
    const result = await deleteFormField(fieldId);
    
    if (result.error) {
      return json({ error: result.error }, { status: 500 });
    }

    return json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/events/[eventId]/form-fields/[fieldId]:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
