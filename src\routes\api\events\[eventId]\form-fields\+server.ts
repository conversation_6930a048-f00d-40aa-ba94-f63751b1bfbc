// API routes for form field management

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { 
  getEventFormFields, 
  createForm<PERSON>ield, 
  initializeStandardFields,
  validateField<PERSON>ey 
} from '$lib/server/formFields';
import type { FormFieldCreate } from '$lib/types/formFields';

// GET /api/events/[eventId]/form-fields
export const GET: RequestHandler = async ({ params, locals: { supabase } }) => {
  try {
    const { eventId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Get form fields
    const result = await getEventFormFields(eventId);
    
    if (result.error) {
      return json({ error: result.error }, { status: 500 });
    }

    // If no fields exist, initialize standard fields
    if (!result.data || result.data.length === 0) {
      const initResult = await initializeStandardFields(eventId);
      if (initResult.success) {
        // Fetch fields again after initialization
        const newResult = await getEventFormFields(eventId);
        return json({ data: newResult.data || [] });
      }
    }

    return json({ data: result.data || [] });
  } catch (error) {
    console.error('Error in GET /api/events/[eventId]/form-fields:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

// POST /api/events/[eventId]/form-fields
export const POST: RequestHandler = async ({ params, request, locals: { supabase } }) => {
  try {
    const { eventId } = params;

    // Check if user is authenticated and is an admin
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin access
    const { data: adminData, error: adminError } = await supabase
      .from('admins')
      .select('id')
      .eq('auth_id', session.user.id)
      .single();

    if (adminError || !adminData) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify event exists and user has access
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('id, created_by')
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      return json({ error: 'Event not found' }, { status: 404 });
    }

    // Parse request body
    const fieldData: FormFieldCreate = await request.json();

    // Validate required fields
    if (!fieldData.fieldKey || !fieldData.fieldLabel || !fieldData.fieldType) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate field key uniqueness
    const keyValidation = await validateFieldKey(eventId, fieldData.fieldKey);
    if (!keyValidation.isValid) {
      return json({ error: 'Field key already exists' }, { status: 400 });
    }

    // Set eventId from params
    fieldData.eventId = eventId;

    // Create the field
    const result = await createFormField(fieldData);
    
    if (result.error) {
      return json({ error: result.error }, { status: 500 });
    }

    return json({ data: result.data }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/events/[eventId]/form-fields:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
