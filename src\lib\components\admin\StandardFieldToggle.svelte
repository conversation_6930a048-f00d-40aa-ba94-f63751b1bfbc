<script lang="ts">
	import { Switch } from '$lib/components/ui/switch';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import { Settings, Info } from 'lucide-svelte';
	import type { StandardField, FormField } from '$lib/types/formFields';

	// Props
	export let standardFieldConfig: StandardField;
	export let field: FormField | undefined;
	export let onToggle: (enabled: boolean) => void;
	export let onEdit: () => void;

	// Reactive values
	$: isEnabled = field?.isEnabled ?? standardFieldConfig.defaultEnabled;
	$: isRequired = field?.isRequired ?? standardFieldConfig.isRequired;
	$: displayOrder = field?.displayOrder ?? standardFieldConfig.displayOrder;

	function handleToggle() {
		onToggle(!isEnabled);
	}
</script>

<div class="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
	<div class="flex items-center gap-4">
		<!-- Toggle Switch -->
		<Switch
			checked={isEnabled}
			onCheckedChange={handleToggle}
			aria-label="Toggle {standardFieldConfig.fieldLabel}"
		/>

		<!-- Field Info -->
		<div class="flex-1">
			<div class="flex items-center gap-2 mb-1">
				<span class="font-medium">{standardFieldConfig.fieldLabel}</span>
				
				<!-- Badges -->
				{#if isRequired}
					<Badge variant="destructive" class="text-xs">Required</Badge>
				{/if}
				
				{#if !isEnabled}
					<Badge variant="secondary" class="text-xs">Disabled</Badge>
				{/if}
				
				<Badge variant="outline" class="text-xs">
					{standardFieldConfig.fieldType}
				</Badge>
			</div>
			
			<div class="text-sm text-muted-foreground">
				<p>{standardFieldConfig.description}</p>
				<p class="text-xs mt-1">Field key: <code class="bg-muted px-1 rounded">{standardFieldConfig.fieldKey}</code></p>
			</div>
		</div>
	</div>

	<!-- Actions -->
	<div class="flex items-center gap-2">
		<!-- Info tooltip could be added here -->
		<Button
			variant="ghost"
			size="sm"
			onclick={onEdit}
			disabled={!field}
			title="Edit field settings"
		>
			<Settings class="h-4 w-4" />
		</Button>
	</div>
</div>

<style>
	code {
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.85em;
	}
</style>
