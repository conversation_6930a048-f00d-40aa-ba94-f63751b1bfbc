<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Loader2, AlertCircle } from 'lucide-svelte';
	import { Turnstile } from 'svelte-turnstile';
	import { PUBLIC_SITE_KEY } from '$env/static/public';
	import toast, { Toaster } from 'svelte-hot-french-toast';
	import DynamicFormField from './DynamicFormField.svelte';
	import type { FormField, DynamicFormData } from '$lib/types/formFields';

	// Props
	export let eventId: string;
	export let eventSlug: string = ''; // Add event slug prop
	export let formFields: FormField[] = [];
	export let loading = false;
	export let preview = false;
	export let registrationStatus: any = null; // Add registration status prop

	// State
	let fieldValues: DynamicFormData = {};
	let fieldErrors: Record<string, string[]> = {};
	let submitting = false;
	let submitError: string | null = null;

	// Turnstile and agreement state
	let turnstileToken: boolean = false;
	let agreed = false;

	// Reactive values
	$: enabledFields = formFields
		.filter((field) => field.isEnabled)
		.sort((a, b) => a.displayOrder - b.displayOrder);
	$: requiredFields = enabledFields.filter((field) => field.isRequired);

	// Handle Turnstile verification
	function handleTurnstileVerification(event: CustomEvent<{ token: string }>) {
		turnstileToken = true;
	}

	function handleTurnstileError(event: CustomEvent<{ code: string }>) {
		turnstileToken = false;
		toast.error('Verification failed. Please try again.');
	}

	// Initialize form values
	onMount(() => {
		// Initialize field values with empty values
		enabledFields.forEach((field) => {
			if (field.fieldType === 'checkbox' && field.fieldOptions?.multiple) {
				fieldValues[field.fieldKey] = [];
			} else if (field.fieldType === 'checkbox') {
				fieldValues[field.fieldKey] = false;
			} else {
				fieldValues[field.fieldKey] = '';
			}
		});
	});

	// Handle field value changes
	function handleFieldChange(fieldKey: string, value: any) {
		fieldValues[fieldKey] = value;

		// Clear field error when user starts typing
		if (fieldErrors[fieldKey]) {
			const newErrors = { ...fieldErrors };
			delete newErrors[fieldKey];
			fieldErrors = newErrors;
		}
	}

	// Validate form
	function validateForm(): boolean {
		const errors: Record<string, string[]> = {};
		let isValid = true;

		for (const field of enabledFields) {
			const value = fieldValues[field.fieldKey];
			const fieldErrors: string[] = [];

			// Check required fields
			if (field.isRequired) {
				if (field.fieldType === 'checkbox' && field.fieldOptions?.multiple) {
					if (!Array.isArray(value) || value.length === 0) {
						fieldErrors.push(`${field.fieldLabel} is required`);
					}
				} else if (field.fieldType === 'checkbox') {
					if (value !== true) {
						fieldErrors.push(`${field.fieldLabel} must be checked`);
					}
				} else if (!value || (typeof value === 'string' && value.trim() === '')) {
					fieldErrors.push(`${field.fieldLabel} is required`);
				}
			}

			// Type-specific validation
			if (value && typeof value === 'string' && value.trim() !== '') {
				switch (field.fieldType) {
					case 'email':
						const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
						if (!emailRegex.test(value)) {
							fieldErrors.push('Please enter a valid email address');
						}
						break;

					case 'phone':
						const phoneRegex = /^\+?[0-9]{8,14}$/;
						if (!phoneRegex.test(value.replace(/\s/g, ''))) {
							fieldErrors.push('Please enter a valid phone number');
						}
						break;

					case 'url':
						try {
							new URL(value);
						} catch {
							fieldErrors.push('Please enter a valid URL');
						}
						break;
				}

				// Custom validation rules
				if (field.validationRules) {
					const rules = field.validationRules;

					if (rules.minLength && value.length < rules.minLength) {
						fieldErrors.push(
							`${field.fieldLabel} must be at least ${rules.minLength} characters long`
						);
					}

					if (rules.maxLength && value.length > rules.maxLength) {
						fieldErrors.push(
							`${field.fieldLabel} must be no more than ${rules.maxLength} characters long`
						);
					}

					if (rules.pattern) {
						const regex = new RegExp(rules.pattern);
						if (!regex.test(value)) {
							fieldErrors.push(rules.customMessage || `${field.fieldLabel} format is invalid`);
						}
					}
				}
			}

			// Number validation
			if (field.fieldType === 'number' && value !== '' && value !== null && value !== undefined) {
				const numValue = Number(value);
				if (isNaN(numValue)) {
					fieldErrors.push(`${field.fieldLabel} must be a valid number`);
				} else if (field.validationRules) {
					const rules = field.validationRules;
					if (rules.min !== undefined && numValue < rules.min) {
						fieldErrors.push(`${field.fieldLabel} must be at least ${rules.min}`);
					}
					if (rules.max !== undefined && numValue > rules.max) {
						fieldErrors.push(`${field.fieldLabel} must be no more than ${rules.max}`);
					}
				}
			}

			if (fieldErrors.length > 0) {
				errors[field.fieldKey] = fieldErrors;
				isValid = false;
			}
		}

		fieldErrors = errors;
		return isValid;
	}

	// Handle form submission
	async function handleSubmit(event: Event) {
		event.preventDefault();

		if (preview) {
			alert('This is a preview. Form submission is disabled.');
			return;
		}

		if (!validateForm()) {
			return;
		}

		submitting = true;
		submitError = null;

		try {
			const response = await fetch(`/api/events/${eventId}/form-responses`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					eventId,
					responses: fieldValues
				})
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.error || 'Failed to submit form');
			}

			// Get user's email from form data for redirect
			const emailValue = fieldValues['email'] || fieldValues['Email'] || '';
			const userEmail = Array.isArray(emailValue) ? emailValue[0] : String(emailValue);

			// Redirect to thank you page with correct format
			if (eventSlug && userEmail) {
				window.location.href = `/${eventSlug}/thankyou?email=${encodeURIComponent(userEmail)}&registered=true`;
			} else {
				// Fallback redirect if slug or email is missing
				console.warn('Missing eventSlug or userEmail for redirect:', { eventSlug, userEmail });
				window.location.href = `/events/${eventId}/thank-you`;
			}
		} catch (error) {
			console.error('Form submission error:', error);
			submitError = error instanceof Error ? error.message : 'Failed to submit form';
		} finally {
			submitting = false;
		}
	}

	// Check if form is valid for submission
	$: canSubmit =
		enabledFields.length > 0 &&
		agreed &&
		turnstileToken &&
		requiredFields.every((field) => {
			const value = fieldValues[field.fieldKey];
			if (field.fieldType === 'checkbox' && field.fieldOptions?.multiple) {
				return Array.isArray(value) && value.length > 0;
			} else if (field.fieldType === 'checkbox') {
				return value === true;
			} else {
				return value && (typeof value !== 'string' || value.trim() !== '');
			}
		});
</script>

<div class="mx-auto max-w-2xl">
	{#if preview}
		<Alert class="mb-6">
			<AlertCircle class="h-4 w-4" />
			<AlertDescription>
				This is a preview of your registration form. Form submission is disabled.
			</AlertDescription>
		</Alert>
	{/if}

	<Card>
		<CardHeader>
			<CardTitle>Registration Form</CardTitle>
			{#if !preview}
				<CardDescription>
					Please fill out all required fields to complete your registration.
				</CardDescription>
			{/if}
		</CardHeader>
		<CardContent>
			{#if loading}
				<div class="flex items-center justify-center py-8">
					<Loader2 class="mr-2 h-6 w-6 animate-spin" />
					<span>Loading form...</span>
				</div>
			{:else if !preview && registrationStatus && !registrationStatus.isOpen}
				<div class="py-8 text-center text-muted-foreground">
					<p class="text-lg font-medium text-red-600">Registration is currently closed</p>
					<p class="mt-2 text-sm">{registrationStatus.reason}</p>
				</div>
			{:else if enabledFields.length === 0}
				<div class="py-8 text-center text-muted-foreground">
					<p>No form fields have been configured for this event.</p>
					{#if preview}
						<p class="mt-2 text-sm">Add fields using the Form Builder to see them here.</p>
					{/if}
				</div>
			{:else}
				<form on:submit={handleSubmit} class="space-y-6">
					{#each enabledFields as field (field.id)}
						<DynamicFormField
							{field}
							value={fieldValues[field.fieldKey]}
							errors={fieldErrors[field.fieldKey] || []}
							onChange={(value) => handleFieldChange(field.fieldKey, value)}
						/>
					{/each}

					{#if !preview}
						<!-- Registration Declaration (Terms and Conditions) -->
						<div class="space-y-4 border-t pt-6">
							<div class="flex items-start space-x-3">
								<input
									type="checkbox"
									id="agreement"
									bind:checked={agreed}
									class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
								/>
								<label for="agreement" class="cursor-pointer text-sm text-gray-700">
									By registering you agree to our terms and conditions and privacy policy.
								</label>
							</div>
						</div>

						<!-- Turnstile Verification -->
						<div class="space-y-2">
							<Turnstile
								theme="light"
								siteKey={PUBLIC_SITE_KEY}
								on:verify={handleTurnstileVerification}
								on:error={handleTurnstileError}
							/>
						</div>
					{/if}

					{#if submitError}
						<Alert variant="destructive">
							<AlertCircle class="h-4 w-4" />
							<AlertDescription>{submitError}</AlertDescription>
						</Alert>
					{/if}

					{#if !preview}
						<div class="flex justify-end pt-4">
							<Button type="submit" disabled={!canSubmit || submitting} class="min-w-[120px]">
								{#if submitting}
									<Loader2 class="mr-2 h-4 w-4 animate-spin" />
									Submitting...
								{:else}
									Register
								{/if}
							</Button>
						</div>
					{/if}

					<div class="text-sm text-muted-foreground">
						<span class="text-red-500">*</span> Required fields
					</div>
				</form>
			{/if}
		</CardContent>
	</Card>
</div>

<Toaster />
