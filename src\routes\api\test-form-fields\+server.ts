// Test API endpoint to verify form field functionality
// This can be used for testing during development

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { 
  initializeStandardFields, 
  getEventFormFields, 
  createFormField 
} from '$lib/server/formFields';
import type { FormFieldCreate } from '$lib/types/formFields';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { action, eventId, ...data } = await request.json();

    switch (action) {
      case 'initialize':
        if (!eventId) {
          return json({ error: 'eventId is required' }, { status: 400 });
        }
        
        const initResult = await initializeStandardFields(eventId);
        return json(initResult);

      case 'get-fields':
        if (!eventId) {
          return json({ error: 'eventId is required' }, { status: 400 });
        }
        
        const fieldsResult = await getEventFormFields(eventId);
        return json(fieldsResult);

      case 'create-custom-field':
        if (!eventId) {
          return json({ error: 'eventId is required' }, { status: 400 });
        }
        
        const fieldData: FormFieldCreate = {
          eventId,
          fieldKey: data.fieldKey || 'test_field',
          fieldLabel: data.fieldLabel || 'Test Field',
          fieldType: data.fieldType || 'text',
          isRequired: data.isRequired || false,
          isStandardField: false,
          isEnabled: true,
          displayOrder: data.displayOrder || 100
        };
        
        const createResult = await createFormField(fieldData);
        return json(createResult);

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Test API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
